@props(['title' => null])

<aside 
    x-data="{ open: false }"
    x-show="open"
    @sidebar-toggle.window="open = !open"
    class="fixed inset-y-0 left-0 z-30 w-64 border-r border-gray-200 bg-white pt-4 transition-transform duration-200 ease-in-out dark:border-gray-800 dark:bg-gray-900 lg:static lg:block lg:translate-x-0"
    :class="{ '-translate-x-full': !open }"
>
    <!-- Logo -->
    <div class="flex items-center justify-between px-4">
        <a href="{{ route('dashboard') }}" class="flex items-center space-x-3">
            <x-application-logo class="h-8 w-auto" />
            <span class="text-lg font-semibold text-gray-800 dark:text-white/90">
                {{ config('app.name', 'Water Management') }}
            </span>
        </a>
        <button @click="open = false" class="lg:hidden">
            <svg class="h-6 w-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="mt-6">
        <div class="space-y-1 px-2">
            <!-- Dashboard -->
            @can('view-manager-dashboard')
            <a 
                href="{{ route('management.dashboard') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('dashboard') || request()->routeIs('management.dashboard') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                {{ __('Management Dashboard') }}
            </a>
            @elseifcan('view-reviewer-dashboard')
            <a 
                href="{{ route('reviewer.dashboard') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('dashboard') || request()->routeIs('reviewer.dashboard') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                {{ __('Reviewer Dashboard') }}
            </a>
            @elseifcan('view-caretaker-dashboard')
            <a 
                href="{{ route('caretaker.dashboard') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('dashboard') || request()->routeIs('caretaker.dashboard') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                </svg>
                {{ __('Caretaker Dashboard') }}
            </a>
            @else
            <a 
                href="{{ route('dashboard') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('dashboard') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                {{ __('Dashboard') }}
            </a>
            @endcan

            <!-- Water Management -->
            @canany(['estates.view_all', 'estates.view_assigned'])
            <a 
                href="{{ auth()->user()->hasRole('admin') ? route('estates') : (auth()->user()->hasRole('manager') ? route('management.estates') : (auth()->user()->hasRole('reviewer') ? route('reviewer.estates') : route('caretaker.estates'))) }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('estates*') || request()->routeIs('management.estates*') || request()->routeIs('reviewer.estates*') || request()->routeIs('caretaker.estates*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                {{ __('Estates') }}
            </a>
            @endcan

            @canany(['analytics.view_all', 'analytics.view_assigned'])
            <a 
                href="{{ auth()->user()->hasRole('admin') ? route('estates.analytics') : route('management.estates.analytics') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('estates.analytics*') || request()->routeIs('management.estates.analytics*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                {{ __('Estate Analytics') }}
            </a>
            @endcan

            @canany(['houses.view_all', 'houses.view_assigned', 'houses.view_own'])
            <a 
                href="{{ auth()->user()->hasRole('admin') ? route('houses') : (auth()->user()->hasRole('manager') ? route('management.houses') : (auth()->user()->hasRole('reviewer') ? route('reviewer.houses') : route('caretaker.houses', ['estateId' => auth()->user()->assignedEstates()->first()->id ?? '']))) }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('houses*') || request()->routeIs('management.houses*') || request()->routeIs('reviewer.houses*') || request()->routeIs('caretaker.houses*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                {{ __('Houses') }}
            </a>
            @endcan

            @canany(['contacts.view_all', 'contacts.view_assigned', 'contacts.view_own'])
            <a 
                href="{{ auth()->user()->hasRole('admin') ? route('contacts') : (auth()->user()->hasRole('manager') ? route('management.contacts') : (auth()->user()->hasRole('reviewer') ? route('reviewer.contacts') : route('caretaker.contacts'))) }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('contacts*') || request()->routeIs('management.contacts*') || request()->routeIs('reviewer.contacts*') || request()->routeIs('caretaker.contacts*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                {{ __('Contacts') }}
            </a>
            @endcan

            <!-- Water Operations -->
            @canany(['readings.view_all', 'readings.view_assigned', 'readings.view_own', 'readings.create_all', 'readings.create_assigned', 'readings.review_all', 'readings.review_assigned', 'readings.approve_all', 'readings.approve_assigned'])
            <div class="px-3 pt-4">
                <h3 class="text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    {{ __('Water Operations') }}
                </h3>
            </div>

            @canany(['readings.create_all', 'readings.create_assigned'])
            <a 
                href="{{ auth()->user()->isAdmin() ? route('readings.create') : (auth()->user()->isCaretaker() ? route('caretaker.readings.create') : route('readings.create')) }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('readings.create*') || request()->routeIs('caretaker.readings.create*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                {{ __('Record Readings') }}
            </a>
            @endcan

            @canany(['readings.review_all', 'readings.review_assigned', 'readings.approve_all', 'readings.approve_assigned'])
            <a 
                href="{{ auth()->user()->isAdmin() ? route('readings.review') : (auth()->user()->isReviewer() ? route('reviewer.readings.pending') : route('readings.review')) }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('readings.review*') || request()->routeIs('reviewer.readings.pending*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {{ __('Review Readings') }}
                @if(isset($pendingReviews) && $pendingReviews > 0)
                    <span class="ml-auto inline-flex items-center rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-200">
                        {{ $pendingReviews }}
                    </span>
                @endif
            </a>
            @endcan

            @canany(['readings.view_all', 'readings.view_assigned', 'readings.view_own'])
            <a 
                href="{{ auth()->user()->isAdmin() ? route('readings.index') : (auth()->user()->isCaretaker() ? route('caretaker.readings') : (auth()->user()->isReviewer() ? route('reviewer.readings') : route('resident.readings'))) }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('readings.index*') || request()->routeIs('caretaker.readings*') || request()->routeIs('reviewer.readings*') || request()->routeIs('resident.readings*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                {{ __('All Readings') }}
            </a>
            @endcan

            @canany(['analytics.view_all', 'analytics.view_assigned', 'analytics.view_own'])
            <a 
                href="{{ route('analytics') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('analytics*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18v4H3V4z" />
                </svg>
                {{ __('Analytics') }}
            </a>
            @endcan
            @endcanany

            <!-- Billing & Reports -->
            @canany(['invoices.view_all', 'invoices.view_assigned', 'invoices.view_own', 'reports.view_all', 'reports.view_assigned', 'reports.view_own', 'export.data_all', 'export.data_assigned', 'export.data_own'])
            <div class="px-3 pt-4">
                <h3 class="text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    {{ __('Billing & Reports') }}
                </h3>
            </div>

            @canany(['invoices.view_all', 'invoices.view_assigned', 'invoices.view_own'])
            <a 
                href="{{ auth()->user()->isAdmin() ? route('billing.index') : (auth()->user()->isReviewer() ? route('reviewer.billing') : (auth()->user()->isResident() ? route('resident.invoices') : route('billing.index'))) }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('billing*') || request()->routeIs('reviewer.billing*') || request()->routeIs('resident.invoices*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {{ __('Billing') }}
            </a>
            @endcan

            @canany(['reports.view_all', 'reports.view_assigned', 'reports.view_own'])
            @if(auth()->user()->isAdmin())
                <!-- Admin Reports Dropdown -->
                <div x-data="{ open: false }" class="relative">
                    <button 
                        @click="open = !open"
                        class="group flex items-center w-full rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                            {{ request()->routeIs('admin.reports*') ? 
                               'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                               'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
                    >
                        <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        {{ __('Reports') }}
                        <svg class="ml-auto h-4 w-4 transform transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    
                    <div 
                        x-show="open" 
                        @click.away="open = false"
                        x-transition:enter="transition ease-out duration-100"
                        x-transition:enter-start="transform opacity-0 scale-95"
                        x-transition:enter-end="transform opacity-100 scale-100"
                        x-transition:leave="transition ease-in duration-75"
                        x-transition:leave-start="transform opacity-100 scale-100"
                        x-transition:leave-end="transform opacity-0 scale-95"
                        class="absolute left-0 z-10 mt-2 w-48 origin-top-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-gray-800"
                    >
                        <div class="py-1">
                            <a 
                                href="{{ route('admin.reports.aging') }}" 
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700
                                    {{ request()->routeIs('admin.reports.aging') ? 'bg-gray-100 dark:bg-gray-700' : '' }}"
                            >
                                {{ __('Aging Report') }}
                            </a>
                            <a 
                                href="{{ route('admin.reports.revenue') }}" 
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700
                                    {{ request()->routeIs('admin.reports.revenue') ? 'bg-gray-100 dark:bg-gray-700' : '' }}"
                            >
                                {{ __('Revenue Report') }}
                            </a>
                            <a 
                                href="{{ route('admin.reports.customer-statement') }}" 
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700
                                    {{ request()->routeIs('admin.reports.customer-statement') ? 'bg-gray-100 dark:bg-gray-700' : '' }}"
                            >
                                {{ __('Customer Statements') }}
                            </a>
                        </div>
                    </div>
                </div>
            @else
                <a 
                    href="{{ auth()->user()->isReviewer() ? route('reviewer.billing.reports') : (auth()->user()->isManager() ? route('management.reports') : route('resident.reports')) }}" 
                    class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                        {{ request()->routeIs('reviewer.billing.reports*') || request()->routeIs('management.reports*') || request()->routeIs('resident.reports*') ? 
                           'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                           'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
                >
                    <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {{ __('Reports') }}
                </a>
            @endif
            @endcan

            @canany(['export.data_all', 'export.data_assigned', 'export.data_own'])
            <a 
                href="{{ route('exports.index') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('exports*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                {{ __('Data Export') }}
            </a>
            @endcan
            @endcanany

            <!-- System Administration -->
            @canany(['users.view_all', 'users.manage_all', 'users.create_all', 'users.edit_all', 'users.delete_all', 'users.assign_estates', 'users.assign_roles', 'system.settings.view', 'system.settings.manage', 'audit.logs.view', 'audit.logs.export'])
            <div class="px-3 pt-4">
                <h3 class="text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    {{ __('Administration') }}
                </h3>
            </div>

            @canany(['users.view_all', 'users.manage_all', 'users.create_all', 'users.edit_all', 'users.delete_all', 'users.assign_estates', 'users.assign_roles'])
            <a 
                href="{{ route('admin.users') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('admin.users*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                {{ __('User Management') }}
            </a>
            @endcan

            @canany(['system.settings.view', 'system.settings.manage'])
            <a 
                href="{{ route('admin.settings') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('admin.settings*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {{ __('System Settings') }}
            </a>
            @endcan

            @canany(['audit.logs.view', 'audit.logs.export'])
            <a 
                href="{{ route('admin.audit') }}" 
                class="group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
                    {{ request()->routeIs('admin.audit*') ? 
                       'bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400' : 
                       'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"
            >
                <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {{ __('Audit Logs') }}
            </a>
            @endcan
            @endcanany
        </div>
    </nav>

    <!-- User Menu -->
    <div class="mt-auto px-4 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                    {{ auth()->user()->initials() }}
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ auth()->user()->name }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ auth()->user()->email }}</p>
                </div>
            </div>
            <a 
                href="{{ route('logout') }}" 
                onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
                class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
            </a>
            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="hidden">
                @csrf
            </form>
        </div>
    </div>
</aside>
