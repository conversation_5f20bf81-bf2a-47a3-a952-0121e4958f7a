@props(['title' => null])

<aside 
    :class="sidebarToggle ? 'translate-x-0 lg:w-64' : '-translate-x-full lg:w-[90px]'"
    class="sidebar fixed left-0 top-0 z-30 flex h-screen flex-col overflow-y-hidden border-r border-gray-200 bg-white px-5 duration-300 ease-linear dark:border-gray-800 dark:bg-black lg:static lg:translate-x-0"
>
    
    <!-- Logo Section -->
    <div
        :class="sidebarToggle ? 'justify-between' : 'justify-center'"
        class="sidebar-header flex items-center gap-2 pb-7 pt-8"
    >
        <a href="{{ route('dashboard') }}">
            <span class="logo" :class="sidebarToggle ? '' : 'hidden'">
                <x-application-logo class="h-8 w-auto" />
                <span class="text-lg font-semibold text-gray-800 dark:text-white/90">
                    {{ config('app.name', 'Water Management') }}
                </span>
            </span>

            <span
                class="logo-icon"
                :class="sidebarToggle ? 'hidden' : 'lg:block'"
            >
                <x-application-logo class="h-8 w-auto" />
            </span>
        </a>
        
        <button @click="sidebarToggle = !sidebarToggle" class="lg:hidden">
            <svg class="h-6 w-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    <!-- Navigation Section -->
    <div class="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
        <nav class="mt-6">
            <div class="space-y-1 px-2">
                <!-- Dashboard -->
                <x-navigation.nav-link href="{{ route('reviewer.dashboard') }}" 
                            icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>' 
                            title="Reviewer Dashboard" 
                        permission="view-reviewer-dashboard"
                        :activeRoutes="['reviewer.dashboard*']" />
            
            <!-- Billing Management -->
            <div class="px-3 pt-4">
                <h3 class="text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    {{ __('Billing Management') }}
                </h3>
            </div>
            
            <x-navigation.nav-link href="{{ route('reviewer.billing') }}" 
                        icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z" /></svg>' 
                        title="Invoices" 
                        permission="invoices.view_assigned"
                        :activeRoutes="['reviewer.billing*']" />
            
            <x-navigation.nav-link href="{{ route('reviewer.billing.reports') }}" 
                        icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>' 
                        title="Invoice Reports" 
                        permission="invoices.export_assigned"
                        :activeRoutes="['reviewer.billing.reports*']" />
            
            <!-- Reading Review -->
            <div class="px-3 pt-4">
                <h3 class="text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    {{ __('Reading Review') }}
                </h3>
            </div>
            
            <x-navigation.nav-link href="{{ route('reviewer.readings') }}" 
                        icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" /></svg>' 
                        title="All Readings" 
                        permission="readings.view_assigned"
                        :activeRoutes="['reviewer.readings*']" />
            
            <x-navigation.nav-link href="{{ route('reviewer.readings.pending') }}" 
                        icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>' 
                        title="Pending Review" 
                        permission="readings.approve_assigned"
                        :activeRoutes="['reviewer.readings.pending*']"
                        badge="{{ isset($pendingReviews) ? $pendingReviews : '' }}" />
            
            <!-- Estate Information -->
            <div class="px-3 pt-4">
                <h3 class="text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    {{ __('Estate Information') }}
                </h3>
            </div>
            
            <x-navigation.nav-link href="{{ route('reviewer.estates') }}" 
                        icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" /></svg>' 
                        title="Estates" 
                        permission="estates.view_assigned"
                        :activeRoutes="['estates*']" />
            
            <x-navigation.nav-link href="{{ route('houses') }}" 
                        icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /></svg>' 
                        title="Houses" 
                        permission="houses.view_assigned"
                        :activeRoutes="['houses*']" />
            
            <x-navigation.nav-link href="{{ route('contacts') }}" 
                        icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283-.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>' 
                        title="Contacts" 
                        permission="contacts.view_assigned"
                        :activeRoutes="['contacts*']" />
            
            <!-- Reports & Exports -->
            <div class="px-3 pt-4">
                <h3 class="text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    {{ __('Reports & Exports') }}
                </h3>
            </div>
            
            <x-navigation.nav-link href="{{ route('reports.index') }}" 
                        icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>' 
                        title="Reports" 
                        permission="reports.view_assigned"
                        :activeRoutes="['reports*']" />
            
            <x-navigation.nav-link href="{{ route('exports.index') }}" 
                        icon='<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg>' 
                        title="Data Export" 
                        permission="export.data_assigned"
                        :activeRoutes="['exports*']" />
        </div>
    </nav>

    <!-- User Menu Section -->
    <div class="mt-auto px-4 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                    {{ auth()->user()->initials() }}
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ auth()->user()->name }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ auth()->user()->email }}</p>
                </div>
            </div>
            <a 
                href="{{ route('logout') }}" 
                onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
                class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
            </a>
            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="hidden">
                @csrf
            </form>
        </div>
    </div>
</aside>