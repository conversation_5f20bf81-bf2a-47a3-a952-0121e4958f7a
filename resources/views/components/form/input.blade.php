@props(['name', 'id' => null, 'type' => 'text', 'value' => '', 'required' => false, 'disabled' => false, 'readonly' => false, 'placeholder' => '', 'wire:model' => null, 'wire:model.lazy' => null, 'wire:model.live' => null, 'wire:model.debounce' => null, 'class' => ''])

@php
    $inputId = $id ?? $name;
    $inputClass = 'block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed ' . $class;
    
    // Handle wire model binding
    $wireAttributes = '';
    if ($wireModel) {
        $wireAttributes = "wire:model=\"{$wireModel}\"";
    } elseif ($wireModelLazy) {
        $wireAttributes = "wire:model.lazy=\"{$wireModelLazy}\"";
    } elseif ($wireModelLive) {
        $wireAttributes = "wire:model.live=\"{$wireModelLive}\"";
    } elseif ($wireModelDebounce) {
        $wireAttributes = "wire:model.debounce=\"{$wireModelDebounce}\"";
    }
@endphp

<div>
    @if($name)
        <label for="{{ $inputId }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ $name }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
    @endif
    
    <input 
        type="{{ $type }}" 
        id="{{ $inputId }}" 
        name="{{ $name }}"
        value="{{ $value }}"
        placeholder="{{ $placeholder }}"
        {{ $required ? 'required' : '' }}
        {{ $disabled ? 'disabled' : '' }}
        {{ $readonly ? 'readonly' : '' }}
        class="{{ $inputClass }}"
        {{ $wireAttributes }}
    />
    
    @error($name)
        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
    @enderror
</div>