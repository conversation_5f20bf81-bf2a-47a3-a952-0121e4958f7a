@props([
    'variant' => 'primary',
    'size' => 'base',
    'icon' => null,
    'href' => null,
    'wireClick' => null,
])

@php
    $variants = [
        'primary' => 'bg-water-600 hover:bg-water-700 text-white',
        'ghost' => 'bg-transparent hover:bg-zinc-100 dark:hover:bg-zinc-700 text-zinc-700 dark:text-zinc-300',
    ];
    
    $sizes = [
        'sm' => 'px-3 py-1.5 text-sm',
        'base' => 'px-4 py-2 text-sm',
    ];
    
    $variantClass = $variants[$variant] ?? $variants['primary'];
    $sizeClass = $sizes[$size] ?? $sizes['base'];
    
    $iconClass = $icon ? 'inline-flex items-center gap-2' : '';
    
    $tag = $href ? 'a' : 'button';
@endphp

<{{ $tag }} 
    @if($href) href="{{ $href }}" @endif
    @if($wireClick) wire:click="{{ $wireClick }}" @endif
    {{ $attributes->merge([
        'class' => "inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-water-500 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none {$variantClass} {$sizeClass} {$iconClass}"
    ]) }}
>
    @if($icon)
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            @if($icon === 'arrow-clockwise')
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 4v4l-4-4m0 16v-4l4 4m-8-8l-4-4m0 0l4 4m0-4v4m8-4h-4m4 0h4m-4 4v4m0-4l-4-4m4 4l4-4" />
            @elseif($icon === 'arrow-down-tray')
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
            @endif
        </svg>
    @endif
    {{ $slot }}
</{{ $tag }}>