<div>
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">System Settings</h1>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Configure system-wide settings and preferences</p>
            </div>
            
            <div class="flex gap-2">
                <x-button 
                    wire:click="resetToDefaults" 
                    variant="ghost"
                    icon="arrow-path"
                >
                    Reset to Defaults
                </x-button>
                <x-button 
                    wire:click="saveSettings" 
                    variant="primary"
                    icon="check"
                >
                    Save Settings
                </x-button>
            </div>
        </div>
    </div>

    <div>
        {{-- Flash Messages --}}
        @if (session()->has('message'))
            <div class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <p class="text-sm text-green-800 dark:text-green-200">{{ session('message') }}</p>
            </div>
        @endif

        {{-- Settings Tabs --}}
        <div class="space-y-6">
            {{-- Tab Navigation --}}
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="-mb-px flex space-x-8 overflow-x-auto">
                    <button 
                        type="button"
                        wire:click="$set('activeTab', 'general')"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'general' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                    >
                        General
                    </button>
                    <button 
                        type="button"
                        wire:click="$set('activeTab', 'water_rates')"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'water_rates' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                    >
                        Water Rates
                    </button>
                    <button 
                        type="button"
                        wire:click="$set('activeTab', 'billing')"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'billing' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                    >
                        Billing
                    </button>
                    <button 
                        type="button"
                        wire:click="$set('activeTab', 'whatsapp')"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'whatsapp' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                    >
                        WhatsApp
                    </button>
                    <button 
                        type="button"
                        wire:click="$set('activeTab', 'email')"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'email' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                    >
                        Email
                    </button>
                    <button 
                        type="button"
                        wire:click="$set('activeTab', 'system')"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                        :class="activeTab === 'system' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'"
                    >
                        System
                    </button>
                </nav>
            </div>

            {{-- Tab Content --}}
            <div class="space-y-6">
                {{-- General Tab --}}
                @if($activeTab === 'general')
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($settingGroups->get('general', collect()) as $setting)
                                @if($setting->type === 'boolean')
                                    <x-checkbox 
                                        wire:model="settings.{{ $setting->key }}"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    />
                                @elseif($setting->options)
                                    <x-select 
                                        wire:model="settings.{{ $setting->key }}"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    >
                                        @foreach(json_decode($setting->options) as $option)
                                            <option value="{{ $option }}">{{ ucfirst($option) }}</option>
                                        @endforeach
                                    </x-select>
                                @else
                                    <x-input 
                                        wire:model="settings.{{ $setting->key }}"
                                        :type="$setting->type === 'integer' || $setting->type === 'float' ? 'number' : 'text'"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                        :step="$setting->type === 'float' ? '0.01' : '1'"
                                    />
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif

                {{-- Water Rates Tab --}}
                @if($activeTab === 'water_rates')
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($settingGroups->get('water_rates', collect()) as $setting)
                                <x-input 
                                    wire:model="settings.{{ $setting->key }}"
                                    type="number"
                                    step="0.01"
                                    :label="$setting->display_name"
                                    :description="$setting->description"
                                />
                            @endforeach
                        </div>
                    </div>
                @endif

                {{-- Billing Tab --}}
                @if($activeTab === 'billing')
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($settingGroups->get('billing', collect()) as $setting)
                                @if($setting->type === 'boolean')
                                    <x-checkbox 
                                        wire:model="settings.{{ $setting->key }}"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    />
                                @elseif($setting->options)
                                    <x-select 
                                        wire:model="settings.{{ $setting->key }}"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    >
                                        @foreach(json_decode($setting->options) as $option)
                                            <option value="{{ $option }}">{{ ucfirst($option) }}</option>
                                        @endforeach
                                    </x-select>
                                @else
                                    <x-input 
                                        wire:model="settings.{{ $setting->key }}"
                                        :type="$setting->type === 'integer' || $setting->type === 'float' ? 'number' : 'text'"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                        :step="$setting->type === 'float' ? '0.01' : '1'"
                                    />
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif

                {{-- WhatsApp Tab --}}
                @if($activeTab === 'whatsapp')
                    <div class="space-y-6">
                        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                            <p class="text-sm text-blue-800 dark:text-blue-200">Configure WhatsApp integration for sending automated messages and notifications.</p>
                        </div>
                        
                        <div class="grid grid-cols-1 gap-6">
                            @foreach($settingGroups->get('whatsapp', collect()) as $setting)
                                @if($setting->type === 'boolean')
                                    <x-checkbox 
                                        wire:model="settings.{{ $setting->key }}"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    />
                                @else
                                    <x-input 
                                        wire:model="settings.{{ $setting->key }}"
                                        :type="$setting->key === 'whatsapp_token' ? 'password' : 'text'"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    />
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif

                {{-- Email Tab --}}
                @if($activeTab === 'email')
                    <div class="space-y-6">
                        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                            <p class="text-sm text-blue-800 dark:text-blue-200">Configure email settings for system notifications and communications.</p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($settingGroups->get('email', collect()) as $setting)
                                @if($setting->type === 'boolean')
                                    <x-checkbox 
                                        wire:model="settings.{{ $setting->key }}"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    />
                                @else
                                    <x-input 
                                        wire:model="settings.{{ $setting->key }}"
                                        type="email"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    />
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif

                {{-- System Tab --}}
                @if($activeTab === 'system')
                    <div class="space-y-6">
                        <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                            <p class="text-sm text-yellow-800 dark:text-yellow-200">These settings affect system behavior. Changes may require administrator privileges.</p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($settingGroups->get('system', collect()) as $setting)
                                @if($setting->type === 'boolean')
                                    <x-checkbox 
                                        wire:model="settings.{{ $setting->key }}"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    />
                                @else
                                    <x-input 
                                        wire:model="settings.{{ $setting->key }}"
                                        :type="$setting->type === 'integer' ? 'number' : 'text'"
                                        :label="$setting->display_name"
                                        :description="$setting->description"
                                    />
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
