<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class EstateShow extends Component
{
    public Estate $estate;

    public string $selectedTab = 'overview'; // Default tab

    public function mount(Estate $estate): void
    {
        $this->estate = $estate;
    }

    public function getUserRole()
    {
        return Auth::user()->role;
    }

    public function getEstatesRoute()
    {
        $role = $this->getUserRole();

        return match ($role) {
            'MANAGER' => route('management.estates'),
            'REVIEWER' => route('reviewer.estates'),
            'CARETAKER' => route('caretaker.estates'),
            default => route('estates'), // Fallback to generic route
        };
    }

    public function getEstateEditRoute($estate)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'MANAGER' => route('management.estates.edit', $estate),
            'REVIEWER' => route('management.estates.edit', $estate), // Reviewers use management routes for estates
            'CARETAKER' => route('caretaker.estates.edit', $estate),
            default => route('estates.edit', $estate), // Fallback to generic route
        };
    }

    public function getHouseCreateRoute($estateId = null)
    {
        $role = $this->getUserRole();
        $params = $estateId ? ['estate' => $estateId] : [];

        return match ($role) {
            'MANAGER' => route('management.houses.create', $params),
            'REVIEWER' => route('reviewer.houses.create', $params),
            'CARETAKER' => route('caretaker.houses.create', $params),
            default => route('houses.create', $params), // Fallback to generic route
        };
    }

    public function getHousesRoute($estateId = null)
    {
        $role = $this->getUserRole();
        $params = $estateId ? ['estate' => $estateId] : [];

        return match ($role) {
            'MANAGER' => route('management.houses', $params),
            'REVIEWER' => route('reviewer.houses', $params),
            'CARETAKER' => $estateId ? route('caretaker.houses', ['estateId' => $estateId]) : route('caretaker.houses'),
            default => route('houses', $params), // Fallback to generic route
        };
    }

    public function getHouseShowRoute($house)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'MANAGER' => route('management.houses.show', $house),
            'REVIEWER' => route('reviewer.houses.show', $house),
            'CARETAKER' => route('caretaker.houses.show', $house),
            default => route('houses.show', $house), // Fallback to generic route
        };
    }

    public function render()
    {
        $recentHouses = $this->estate->houses()
            ->with('contacts')
            ->latest()
            ->limit(5)
            ->get();

        // Get assigned staff
        $assignedManagers = User::where('role', 'management')
            ->whereHas('assignedEstates', function ($query) {
                $query->where('estate_id', $this->estate->id);
            })
            ->get();

        $assignedReviewers = User::where('role', 'reviewer')
            ->whereHas('assignedEstates', function ($query) {
                $query->where('estate_id', $this->estate->id);
            })
            ->get();

        $assignedCaretakers = User::where('role', 'caretaker')
            ->whereHas('estates', function ($query) {
                $query->where('estate_id', $this->estate->id);
            })
            ->get();

        $stats = [
            'total_houses' => $this->estate->total_houses ?? $this->estate->houses()->count(),
            'occupied_houses' => $this->estate->occupied_houses ?? $this->estate->houses()->whereHas('contacts')->count(),
            'vacant_houses' => ($this->estate->total_houses ?? $this->estate->houses()->count()) - ($this->estate->occupied_houses ?? $this->estate->houses()->whereHas('contacts')->count()),
            'occupancy_rate' => ($this->estate->total_houses ?? $this->estate->houses()->count()) > 0
                ? round((($this->estate->occupied_houses ?? $this->estate->houses()->whereHas('contacts')->count()) / ($this->estate->total_houses ?? $this->estate->houses()->count())) * 100, 1)
                : 0,
        ];

        return view('livewire.estate-show', [
            'recentHouses' => $recentHouses,
            'stats' => $stats,
            'assignedManagers' => $assignedManagers,
            'assignedReviewers' => $assignedReviewers,
            'assignedCaretakers' => $assignedCaretakers,
        ]);
    }
}
