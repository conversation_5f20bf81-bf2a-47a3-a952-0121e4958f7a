<?php

namespace App\Livewire\Admin;

use App\Models\ActivityLog;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class AuditLogs extends Component
{
    use WithPagination;

    #[Url]
    public string $search = '';

    #[Url]
    public string $userFilter = 'all';

    #[Url]
    public string $actionFilter = 'all';

    #[Url]
    public string $entityFilter = 'all';

    #[Url]
    public string $dateFrom = '';

    #[Url]
    public string $dateTo = '';

    #[Url]
    public string $sortBy = 'created_at';

    #[Url]
    public string $sortDirection = 'desc';

    public bool $showDetailsModal = false;

    public ?ActivityLog $selectedLog = null;

    public function mount(): void
    {
        if (empty($this->dateFrom)) {
            $this->dateFrom = Carbon::now()->subDays(30)->format('Y-m-d');
        }
        if (empty($this->dateTo)) {
            $this->dateTo = Carbon::now()->format('Y-m-d');
        }
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    public function updatedUserFilter(): void
    {
        $this->resetPage();
    }

    public function updatedActionFilter(): void
    {
        $this->resetPage();
    }

    public function updatedEntityFilter(): void
    {
        $this->resetPage();
    }

    public function updatedDateFrom(): void
    {
        $this->resetPage();
    }

    public function updatedDateTo(): void
    {
        $this->resetPage();
    }

    public function sortBy(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'desc';
        }
        $this->resetPage();
    }

    public function clearFilters(): void
    {
        $this->search = '';
        $this->userFilter = 'all';
        $this->actionFilter = 'all';
        $this->entityFilter = 'all';
        $this->dateFrom = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->dateTo = Carbon::now()->format('Y-m-d');
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->resetPage();
    }

    public function showDetails(ActivityLog $log): void
    {
        $this->selectedLog = $log;
        $this->showDetailsModal = true;
    }

    public function closeModal(): void
    {
        $this->showDetailsModal = false;
        $this->selectedLog = null;
    }

    public function exportLogs(): void
    {
        // This would trigger an export job
        session()->flash('message', 'Export started. You will be notified when it\'s ready.');
    }

    public function render()
    {
        $query = ActivityLog::with('user');

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('description', 'like', '%'.$this->search.'%')
                    ->orWhere('action', 'like', '%'.$this->search.'%')
                    ->orWhere('entity_type', 'like', '%'.$this->search.'%');
            });
        }

        // Apply user filter
        if ($this->userFilter !== 'all') {
            $query->where('user_id', $this->userFilter);
        }

        // Apply action filter
        if ($this->actionFilter !== 'all') {
            $query->where('action', $this->actionFilter);
        }

        // Apply entity filter
        if ($this->entityFilter !== 'all') {
            $query->where('entity_type', $this->entityFilter);
        }

        // Apply date filters
        if ($this->dateFrom) {
            $query->whereDate('created_at', '>=', $this->dateFrom);
        }
        if ($this->dateTo) {
            $query->whereDate('created_at', '<=', $this->dateTo);
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        $logs = $query->paginate(20);

        // Get filter options
        $users = User::orderBy('name')->get();
        $actions = ActivityLog::distinct()->pluck('action')->filter()->sort();
        $entityTypes = ActivityLog::distinct()->pluck('entity_type')->filter()->sort();

        return view('livewire.admin.audit-logs', [
            'logs' => $logs,
            'users' => $users,
            'actions' => $actions,
            'entityTypes' => $entityTypes,
        ]);
    }
}
