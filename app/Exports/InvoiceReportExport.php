<?php

namespace App\Exports;

use App\Models\Invoice;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class InvoiceReportExport implements FromCollection, WithHeadings, WithMapping
{
    protected $invoices;

    public function __construct($invoices)
    {
        $this->invoices = $invoices;
    }

    public function collection()
    {
        return $this->invoices;
    }

    public function headings(): array
    {
        return [
            'Invoice ID',
            'House Number',
            'Estate',
            'Billing Period',
            'Consumption (L)',
            'Amount (KES)',
            'Status',
            'Created At',
            'Sent At',
            'Paid At',
        ];
    }

    public function map($invoice): array
    {
        return [
            $invoice->id,
            $invoice->house->house_number,
            $invoice->house->estate->name,
            $invoice->billing_period_start . ' to ' . $invoice->billing_period_end,
            $invoice->consumption,
            $invoice->amount,
            $invoice->status,
            $invoice->created_at->format('Y-m-d H:i:s'),
            $invoice->sent_at ? $invoice->sent_at->format('Y-m-d H:i:s') : 'Not sent',
            $invoice->paid_at ? $invoice->paid_at->format('Y-m-d H:i:s') : 'Not paid',
        ];
    }
}